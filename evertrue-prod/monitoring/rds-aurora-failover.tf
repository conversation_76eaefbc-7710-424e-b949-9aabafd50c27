# Create a CloudWatch alarm for all Aurora failover events
resource "aws_cloudwatch_event_rule" "aurora-failover" {
  name        = "capture-aurora-failover"
  description = "Capture Aurora cluster failover events"

  event_pattern = jsonencode({
    source = [
      "aws.rds"
    ],
    detail-type = [
      "RDS DB Cluster Event"
    ],
    detail = {
      EventCategories = [
        "failover"
      ]
    }
  })
}

resource "aws_cloudwatch_event_target" "alarm-notifications-topic" {
  rule = aws_cloudwatch_event_rule.aurora-failover.id
  arn  = "arn:aws:sns:us-east-1:923017004626:alarm-notifications"
}
