
# Fetching the listener of each ALB
data "aws_lb_listener" "alb_listener" {
  count             = length(var.application_load_balancers)
  load_balancer_arn = data.aws_lb.alb[count.index].arn
  port              = 443
}

# Extracting the target group ARN from each listener's default action
locals {
  target_group_arns = [for listener in data.aws_lb_listener.alb_listener : listener.default_action[0].target_group_arn]
}

# Fetching the target group details using the extracted ARNs
data "aws_lb_target_group" "alb_tg" {
  count = length(var.application_load_balancers)
  arn   = local.target_group_arns[count.index]
}

# CloudWatch Alarms for each Application Load Balancer's target group
resource "aws_cloudwatch_metric_alarm" "alb_tg_alarm" {
  count               = length(var.application_load_balancers)
  alarm_name          = "${data.aws_lb_target_group.alb_tg[count.index].name}-unhealthy-alert"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "5"
  metric_name         = "UnHealthyHostCount"
  namespace           = "AWS/ApplicationELB"
  period              = "60"
  statistic           = "SampleCount"
  threshold           = "1"
  alarm_description   = "This metric checks unhealthy target count for ALB target group"
  alarm_actions       = [var.sns_topic_arn]
  
  dimensions = {
    TargetGroup  = data.aws_lb_target_group.alb_tg[count.index].name
    LoadBalancer = var.application_load_balancers[count.index]
  }
}
