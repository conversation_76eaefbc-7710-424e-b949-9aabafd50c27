locals {
  slack_channels = {
    "ops" = {
      slack_channel_id   = "C029LUXAU"
      slack_workspace_id = "T028E2C1P"
      iam_role_arn       = "arn:aws:iam::923017004626:role/service-role/AWSChatbotRole-"
      guardrail_policies = ["arn:aws:iam::aws:policy/ReadOnlyAccess"]
      sns_topic_arns     = ["arn:aws:sns:us-east-1:923017004626:alarm-notifications"]
    }
  }
}

resource "awscc_chatbot_slack_channel_configuration" "this" {
  for_each = local.slack_channels

  configuration_name = try("slack-${each.key}", "example-slack-channel-config")
  iam_role_arn       = try(each.value.iam_role_arn, "")
  slack_channel_id   = try(each.value.slack_channel_id, "")
  slack_workspace_id = try(each.value.slack_workspace_id, "")
  guardrail_policies = try(each.value.guardrail_policies, ["arn:aws:iam::aws:policy/ReadOnlyAccess"])
  sns_topic_arns     = try(each.value.sns_topic_arns, null)
  user_role_required = try(each.value.user_role_required, false)
}

# resource "awscc_iam_role" "example" {
#   role_name = "ChatBot-Channel-Role"
#   assume_role_policy_document = jsonencode({
#     Version = "2012-10-17"
#     Statement = [
#       {
#         Action = "sts:AssumeRole"
#         Effect = "Allow"
#         Sid    = ""
#         Principal = {
#           Service = "chatbot.amazonaws.com"
#         }
#       },
#     ]
#   })
#   managed_policy_arns = ["arn:aws:iam::aws:policy/AWSResourceExplorerReadOnlyAccess"]
# }
