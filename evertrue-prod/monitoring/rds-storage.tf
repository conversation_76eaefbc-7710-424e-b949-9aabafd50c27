# Fetch details of each individual RDS instance
data "aws_db_instance" "individual" {
  for_each = toset(data.aws_db_instances.all.instance_identifiers)
  db_instance_identifier = each.value
}

resource "aws_cloudwatch_metric_alarm" "rds_free_storage" {
  for_each = toset(data.aws_db_instances.all.instance_identifiers)
  alarm_name = "${each.value}-RDS-FreeStorageSpace"
  comparison_operator = "LessThanOrEqualToThreshold"
  evaluation_periods = "1"
  # Set metric name based on engine type
  metric_name = data.aws_db_instance.individual[each.value].engine == "aurora-mysql" ? "FreeLocalStorage" : "FreeStorageSpace"
  namespace = "AWS/RDS"
  period = "300"
  statistic = "Average"
  threshold = 2000000000  # Back to 2GB in bytes
  alarm_description = "Free storage space alarm for ${each.value}"
  alarm_actions = [aws_sns_topic.rds_alarm.arn]
  dimensions = {
    DBInstanceIdentifier = each.value
  }
  tags = {
    "ManagedBy" = "Terraform"
    "Owner" = "Michael D"
  }
}