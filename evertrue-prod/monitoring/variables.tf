variable "account_name" {
  description = "The name of the AWS account."
  type        = string
  default     = "evertrue-prod" # You can set a default or leave it out to make it mandatory.
}

variable "cpu_utilization_threshold" {
  description = "The threshold for the RDS CPU Utilization alarm."
  type        = number
  default     = 85
}

variable "vpc_name" {
  description = "The name of the VPC"
  type        = string
  default     = "shared-infra-prod"
}

variable "vpc_id" {
  description = "The ID of the VPC"
  type        = string
  default     = "vpc-0ff356fad95cc1291"
}

variable "retention_days" {
  description = "The number of days to retain flow logs"
  type        = number
  default     = 30
}
#--------------------

variable "dynamodb_tables" {
  description = "List of DynamoDB table names"
  type        = list(string)
  default     = [
    "importer-assignment-cache",
    "importer-contact-cache",
    "importer-interaction-cache",
    "importer-proposal-cache",
    "importer-transactional-gift-cache",
    "ugc-request-cache",
    "ai-request-cache",
    "ai-python-request-cache"

  ]
}

#--------------------
variable "application_load_balancers" {
  description = "A list of application load balancer names"
  type        = list(string)
  default     = ["shared-infra-prod", "shared-infra-prod-host", "shared-infra-prod-priv"]
}

variable "sns_topic_arn" {
  description = "ARN of the SNS topic to which alarms will be sent"
  type        = string
  default     = "arn:aws:sns:us-east-1:923017004626:alarm-notifications"
}



#-----------

variable "lambda_functions" {
  description = "Additional list of Lambda function names"
  type        = list(string)
  default     = [
    "emr-scheduled-jobs-prod-enrichment-LambdaFunction-1MGNJPO9YWWSW",
    "emr-scheduled-jobs-prod-graduway-Gr-LambdaFunction-I8OSGZNO4CTS",
    "EnrichmentEsConsumer",
    "ecs-cluster-prod-Lifecycl-LifecycleHandlerFunction-19MXV50BHAKK2",
    "emr-scheduled-jobs-prod-sodas-Faceb-LambdaFunction-129JATYG256IP",
    "sftp-prod-GetUserConfigLambda-15Egcnzx9EZ9",
    "emr-scheduled-jobs-prod-contacts-Co-LambdaFunction-1BH0FZG8VS6GE",
    "importer-worker-ecs-clust-LifecycleHandlerFunction-ReV3b3WskHIU",
    "PublishECSDisconnect",
    "MarkECSNodeUnhealthy"
  ]
}
