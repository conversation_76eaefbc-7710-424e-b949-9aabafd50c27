# Fetching ALB details
data "aws_lb" "alb" {
  count = length(var.application_load_balancers)
  name  = var.application_load_balancers[count.index]
}

# Extracting the LoadBalancer identifier in the desired format from the ALB ARN
locals {
  alb_identifiers = [for arn in data.aws_lb.alb.*.arn : element(split("loadbalancer/", arn), 1)]
}

# CloudWatch Alarms for each Application Load Balancer's 5XX errors
resource "aws_cloudwatch_metric_alarm" "alb_5xx_errors_alarm" {
  count               = length(var.application_load_balancers)
  alarm_name          = "${var.application_load_balancers[count.index]}-5xx-errors-alert"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "5"
  metric_name         = "HTTPCode_ELB_5XX_Count"
  namespace           = "AWS/ApplicationELB"
  period              = "60"
  statistic           = "SampleCount"
  threshold           = "100"
  alarm_description   = "This metric checks for 5XX errors count on the ALB"
  alarm_actions       = [var.sns_topic_arn]

  dimensions = {
    LoadBalancer = local.alb_identifiers[count.index]
  }
}