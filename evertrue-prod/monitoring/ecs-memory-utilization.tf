# ECS Memory Utilization Alarms for api-ecs-cluster-prod
# Monitors memory utilization across all ECS services and alerts when > 90%

locals {
  # List of all ECS services to monitor
  ecs_services = [
    "charting-api-service",
    "exporter-api-service", 
    "asset-manager-service",
    "landing-pages-api-service",
    "ems-api-service",
    "emma-api-service",
    "journeys-api-service",
    "ugc-reader-api-service",
    "skiff-api-service",
    "gangway-api-service",
    "ai-python-api-service",
    "gifts-api-service",
    "hub-api-service",
    "ugc-diff-api-service",
    "events-api-service",
    "et-crm-integrations-api-service",
    "ugc-writer-api-service",
    "datadog-agent-service",
    "kafka-manager-api-service",
    "assignments-api-service",
    "search-python-api-service",
    "contacts-search-api-service",
    "engage-api-service",
    "proxy-api-service",
    "suggestions-api-service",
    "tags-api-service",
    "auth-api-service",
    "graphql-server-service",
    "devsetup-api-service",
    "contacts-writer-api-service",
    "sodas-api-service",
    "graduway-api-service",
    "contacts-reader-api-service",
    "dna-api-service",
    "outreach-api-service",
    "contacts-gifts-api-service",
    "importer-api-service",
    "burrow-api-service",
    "loading-dock-app-service",
    "search-api-service",
    "capillary-api-service",
    "chronometer-api-service",
    "voyager-api-service"
  ]
  
  cluster_name = "api-ecs-cluster-prod"
}

# CloudWatch Alarms for ECS Memory Utilization
resource "aws_cloudwatch_metric_alarm" "ecs_memory_utilization" {
  for_each = toset(local.ecs_services)
  
  alarm_name          = "ECS-MemoryUtilization-${each.value}"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "MemoryUtilization"
  namespace           = "AWS/ECS"
  period              = "300"  # 5 minutes
  statistic           = "Average"
  threshold           = "90"   # 90% memory utilization
  alarm_description   = "This metric monitors memory utilization for ECS service ${each.value}"
  alarm_actions       = [var.sns_topic_arn]
  ok_actions          = [var.sns_topic_arn]
  
  dimensions = {
    ServiceName = each.value
    ClusterName = local.cluster_name
  }
  
  tags = {
    Name        = "ECS-MemoryUtilization-${each.value}"
    Environment = "prod"
    Service     = each.value
    Cluster     = local.cluster_name
    AlertType   = "memory-utilization"
    ManagedBy   = "Terraform"
    Owner       = "Michael D"
  }
}
