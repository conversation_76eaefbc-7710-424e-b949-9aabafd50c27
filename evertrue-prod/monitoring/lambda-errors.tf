resource "aws_cloudwatch_metric_alarm" "lambda_error_rate_alarms" {
  count               = length(var.lambda_functions)
  alarm_name          = "${var.lambda_functions[count.index]}-error-rate-alarm"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "15" # Alarm if error rate is maintained for 15 minutes
  metric_name         = "Errors"
  namespace           = "AWS/Lambda"
  period              = "60"
  statistic           = "Sum"
  threshold           = "2"
  alarm_description   = "This metric checks error rate for Lambda functions"
  alarm_actions       = [var.sns_topic_arn]

  dimensions = {
    FunctionName = var.lambda_functions[count.index]
  }
}
