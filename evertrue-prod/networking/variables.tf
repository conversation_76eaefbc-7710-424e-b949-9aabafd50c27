################################################################################
# Global variables
################################################################################

variable "commonTagsGlobal" {
  type    = map(string)
  default = {}
}


#########shared-infra-stage-subnets########

variable "evertrue_prod_subnets" {
  description = "List of subnet IDs for the evertrue-prod environment"
  type        = list(string)
  default     = [
    "subnet-06f4a38ec8a4d8743",  // Private Subnet (AZ3)
    "subnet-02ce767bbe44e5321",  // Public Subnet (AZ1)
    "subnet-00f4d96c1843f0566",  // Private Subnet (AZ2)
  ]
}