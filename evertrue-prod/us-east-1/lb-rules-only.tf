locals {
  rules = {
    "Interaction File Guide" = {
      listener_arn = "arn:aws:elasticloadbalancing:us-east-1:************:listener/app/shared-infra-prod-host/af9d6426d2448d8f/d56f535f0e7f719f"
      priority     = 1200
      action_type  = "redirect"
      redirect_action = {
        protocol    = "HTTPS"
        port        = 443
        host        = "docs.google.com"
        path        = "/spreadsheets/d/1YJYkGNDTgOxKn3Y66KKUQMzBFRs2DaaekZFfJbO4rQI"
        query       = ""
        status_code = "HTTP_301"
      }
      conditions = {
        host_headers  = "learn.evertrue.com"
        path_patterns = "/interactions/"
      }
    }
    "Relationship Management File Guide" = {
      listener_arn = "arn:aws:elasticloadbalancing:us-east-1:************:listener/app/shared-infra-prod-host/af9d6426d2448d8f/d56f535f0e7f719f"
      priority     = 1201
      action_type  = "redirect"
      redirect_action = {
        protocol    = "HTTPS"
        port        = 443
        host        = "docs.google.com"
        path        = "/spreadsheets/d/1ni6zPpugibcW41c1HDbA0mrp-XtA7J9xabX0RukybZ4"
        query       = ""
        status_code = "HTTP_301"
      }
      conditions = {
        host_headers  = "learn.evertrue.com"
        path_patterns = "/data-rmassignment/"
      }
    }
    "Volunteer Assignment File Guide" = {
      listener_arn = "arn:aws:elasticloadbalancing:us-east-1:************:listener/app/shared-infra-prod-host/af9d6426d2448d8f/d56f535f0e7f719f"
      priority     = 1202
      action_type  = "redirect"
      redirect_action = {
        protocol    = "HTTPS"
        port        = 443
        host        = "docs.google.com"
        path        = "/spreadsheets/d/1zrsmTcFfG_TtI5CoQ7_POyJhzb4dRHHthYExzbitXiM"
        query       = ""
        status_code = "HTTP_301"
      }
      conditions = {
        host_headers  = "learn.evertrue.com"
        path_patterns = "/data-volunteerassignments/"
      }
    }
    "Gift Transactions File Guide" = {
      listener_arn = "arn:aws:elasticloadbalancing:us-east-1:************:listener/app/shared-infra-prod-host/af9d6426d2448d8f/d56f535f0e7f719f"
      priority     = 1203
      action_type  = "redirect"
      redirect_action = {
        protocol    = "HTTPS"
        port        = 443
        host        = "docs.google.com"
        path        = "/spreadsheets/d/1YD1fBWZ2L1KFXJHf91QGKLN2_HqseydAa2TRw_FEC58"
        query       = ""
        status_code = "HTTP_301"
      }
      conditions = {
        host_headers  = "learn.evertrue.com"
        path_patterns = "/data-gifts/"
      }
    }
    "Proposals File Guide" = {
      listener_arn = "arn:aws:elasticloadbalancing:us-east-1:************:listener/app/shared-infra-prod-host/af9d6426d2448d8f/d56f535f0e7f719f"
      priority     = 1204
      action_type  = "redirect"
      redirect_action = {
        protocol    = "HTTPS"
        port        = 443
        host        = "docs.google.com"
        path        = "/spreadsheets/d/19o3bSVRPeOug3NUObufvMS3ZQm58KTSVzYGrX6w8P48/"
        query       = ""
        status_code = "HTTP_301"
      }
      conditions = {
        host_headers  = "learn.evertrue.com"
        path_patterns = "/data-proposals/"
      }
    }
    "Constituent File Guide" = {
      listener_arn = "arn:aws:elasticloadbalancing:us-east-1:************:listener/app/shared-infra-prod-host/af9d6426d2448d8f/d56f535f0e7f719f"
      priority     = 1205
      action_type  = "redirect"
      redirect_action = {
        protocol    = "HTTPS"
        port        = 443
        host        = "docs.google.com"
        path        = "/spreadsheets/d/1PTBo7gj3Owu4KhjDWclGjRjcgesy10FTqtxpYUErUEc/"
        query       = ""
        status_code = "HTTP_301"
      }
      conditions = {
        host_headers  = "learn.evertrue.com"
        path_patterns = "/data-constituent/"
      }
    }
    "Evertrue University" = {
      listener_arn = "arn:aws:elasticloadbalancing:us-east-1:************:listener/app/shared-infra-prod-host/af9d6426d2448d8f/d56f535f0e7f719f"
      priority     = 1206
      action_type  = "redirect"
      redirect_action = {
        protocol    = "HTTPS"
        port        = 443
        host        = "help.evertrue.com"
        path        = "/category/tutorials"
        query       = ""
        status_code = "HTTP_301"
      }
      conditions = {
        host_headers  = "learn.evertrue.com"
        path_patterns = "/evertrue-university/"
      }
    }
  }
}

module "lb_rules" {
  source = "git@et-gh:evertrue/et-terraform.git//modules/lb-rules-only?ref=custom_modules"

  for_each = local.rules

  listener_arn     = try(each.value.listener_arn, "")
  priority         = try(each.value.priority, 0)
  action_type      = try(each.value.action_type, "")
  target_group_arn = try(each.value.target_group_arn, "")
  redirect_action  = try(each.value.redirect_action, {})
  conditions       = try(each.value.conditions, [])
}
