locals {
  policies = {
    "AthenaAccess" = {
      description = "Allows access to the AWS Athena."
      policy      = data.aws_iam_policy_document.AthenaAccess.json
    }
  }
}

module "iam_policy_from_data_source" {
  source  = "terraform-aws-modules/iam/aws//modules/iam-policy"
  version = "5.18.0"

  for_each = local.policies

  name        = each.key
  path        = try(each.value.path, "/")
  description = try(each.value.description, "")
  policy      = try(each.value.policy, "")
  tags        = try(each.value.tags, {})
}
