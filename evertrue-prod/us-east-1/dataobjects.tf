################################################################################
################################################################################
# Data Objects
################################################################################
################################################################################

data "aws_caller_identity" "current" {}

data "aws_region" "current" {}

data "aws_iam_policy_document" "AthenaAccess" {
  statement {
    actions = [
      "athena:GetTableMetadata",
      "athena:StartQueryExecution",
      "athena:GetSession",
      "athena:GetCalculationExecutionCode",
      "athena:GetQueryResults",
      "athena:ListCalculationExecutions",
      "athena:GetDatabase",
      "athena:GetDataCatalog",
      "athena:GetQueryRuntimeStatistics",
      "athena:GetNamedQuery",
      "athena:GetCapacityReservation",
      "athena:ListQueryExecutions",
      "athena:ListNotebookSessions",
      "athena:GetWorkGroup",
      "athena:ListNotebookMetadata",
      "athena:StopQueryExecution",
      "athena:StartCalculationExecution",
      "athena:GetNotebookMetadata",
      "athena:BatchGetPreparedStatement",
      "athena:TerminateSession",
      "athena:StopCalculationExecution",
      "athena:GetQueryResultsStream",
      "athena:GetCalculationExecution",
      "athena:GetPreparedStatement",
      "athena:ListTagsForResource",
      "athena:ListNamedQueries",
      "athena:ListSessions",
      "athena:CreateNamedQuery",
      "athena:ListDatabases",
      "athena:GetCalculationExecutionStatus",
      "athena:GetSessionStatus",
      "athena:GetQueryExecution",
      "athena:GetCapacityAssignmentConfiguration",
      "athena:StartSession",
      "athena:ListTableMetadata",
      "athena:BatchGetNamedQuery",
      "athena:ListPreparedStatements",
      "athena:BatchGetQueryExecution"
    ]

    resources = [
      "arn:aws:athena:*:${data.aws_caller_identity.current.account_id}:workgroup/*",
      "arn:aws:athena:*:${data.aws_caller_identity.current.account_id}:datacatalog/*",
      "arn:aws:athena:*:${data.aws_caller_identity.current.account_id}:capacity-reservation/*"
    ]
  }
  statement {
    actions = [
      "athena:ListApplicationDPUSizes",
      "athena:ListEngineVersions",
      "athena:ListDataCatalogs",
      "athena:GetNamespace",
      "athena:GetQueryExecutions",
      "athena:ListWorkGroups",
      "athena:GetCatalogs",
      "athena:ListCapacityReservations",
      "athena:GetNamespaces",
      "athena:GetExecutionEngine",
      "athena:GetExecutionEngines",
      "athena:GetTables",
      "athena:GetTable",
      "athena:ListExecutors",
      "athena:RunQuery",
    ]

    resources = [
      "*",
    ]
  }
}

################################################################################
# Built by CloudFormation
################################################################################
data "aws_route53_zone" "priv_evertrue" {
  name         = "priv.evertrue.com"
  private_zone = true
}

data "aws_lb" "shared-infra-prod-host" {
  arn  = "arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/shared-infra-prod-host/af9d6426d2448d8f"
  name = "shared-infra-prod-host"
}
