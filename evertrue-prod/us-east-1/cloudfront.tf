# ACM Certificate
data "aws_acm_certificate" "prod_cert" {
  domain       = "*.evertrue.com"
  statuses     = ["ISSUED"]
  most_recent  = true
}

# CloudFront Origin Access Identity
resource "aws_cloudfront_origin_access_identity" "prod_oai" {
  comment = "OAI for et-artifacts-prod CloudFront"
}

# S3 Bucket Policy for OAI access
resource "aws_s3_bucket_policy" "prod_bucket_policy" {
  bucket = "et-artifacts-prod"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid       = "AllowCloudFrontAccess",
        Effect    = "Allow",
        Principal = {
          AWS = "arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity ${aws_cloudfront_origin_access_identity.prod_oai.id}"
        },
        Action    = "s3:GetObject",
        Resource  = "arn:aws:s3:::et-artifacts-prod/*"
      }
    ]
  })
}

# CloudFront Distribution
resource "aws_cloudfront_distribution" "prod_distribution" {
  enabled             = true
  is_ipv6_enabled     = true
  price_class         = "PriceClass_100"
  aliases             = ["artifacts.evertrue.com"]
  comment             = "CloudFront distribution for et-artifacts-prod"
  http_version        = "http2"
  default_root_object = ""

  origin {
    domain_name = "et-artifacts-prod.s3.amazonaws.com"
    origin_id   = "S3-et-artifacts-prod"

    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.prod_oai.cloudfront_access_identity_path
    }
  }

  default_cache_behavior {
    allowed_methods  = ["GET", "HEAD", "OPTIONS"]
    cached_methods   = ["GET", "HEAD"]
    target_origin_id = "S3-et-artifacts-prod"

    forwarded_values {
      query_string = false
      cookies {
        forward = "none"
      }
    }

    viewer_protocol_policy = "redirect-to-https"
    min_ttl                = 0
    default_ttl            = 3600
    max_ttl                = 86400
    compress               = true
  }

  viewer_certificate {
    acm_certificate_arn      = data.aws_acm_certificate.prod_cert.arn
    ssl_support_method       = "sni-only"
    minimum_protocol_version = "TLSv1.2_2021"
  }

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  tags = {
    Name        = "artifacts-prod-distribution"
    Environment = "prod"
  }
}

# Outputs
output "cf_prod_distribution_domain" {
  value = aws_cloudfront_distribution.prod_distribution.domain_name
}

output "cf_prod_distribution_id" {
  value = aws_cloudfront_distribution.prod_distribution.id
}

output "cf_prod_distribution_arn" {
  value = aws_cloudfront_distribution.prod_distribution.arn
}
