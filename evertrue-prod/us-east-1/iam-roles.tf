locals {
  roles = {
    AthenaAccessRole = {
      create_instance_profile = false
      role_requires_mfa       = false

      custom_role_trust_policy = <<EOF
{
  "Version" : "2012-10-17",
  "Statement" : [
    {
      "Effect" : "Allow",
      "Principal" : {
        "AWS" : "arn:aws:iam::640433229668:root"
      },
      "Action" : "sts:AssumeRole",
      "Condition" : {}
    }
  ]
}
EOF
      custom_role_policy_arns = [
        "${module.iam_policy_from_data_source["AthenaAccess"].arn}"

      ]
    }
  }
}

module "iam_assumable_role" {
  source  = "terraform-aws-modules/iam/aws//modules/iam-assumable-role"
  version = "5.18.0"

  for_each = local.roles

  role_name                = each.key
  create_role              = true
  create_instance_profile  = try(each.value.create_instance_profile, false)
  trusted_role_services    = try(each.value.trusted_role_services, [])
  custom_role_trust_policy = try(each.value.custom_role_trust_policy, "")
  role_requires_mfa        = try(each.value.role_requires_mfa, false)
  custom_role_policy_arns  = try(each.value.custom_role_policy_arns, [])
}
