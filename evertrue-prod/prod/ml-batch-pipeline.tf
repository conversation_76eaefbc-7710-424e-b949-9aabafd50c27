# AWS Batch Infrastructure for ML Pipeline - Terraform Configuration

# IAM Role for Batch Jobs
resource "aws_iam_role" "batch_job_role" {
  name = "ml-pipeline-batch-job-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })

  tags = {
    Project     = "ML-Pipeline"
    Environment = "Production"
  }
}

# Attach ECS Task Execution Role Policy to Batch Job Role
resource "aws_iam_role_policy_attachment" "batch_job_role_ecs_policy" {
  role       = aws_iam_role.batch_job_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

# IAM Policy for ML Pipeline Permissions
resource "aws_iam_role_policy" "ml_pipeline_permissions" {
  name = "MLPipelinePermissions"
  role = aws_iam_role.batch_job_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      # S3 permissions
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject",
          "s3:ListBucket"
        ]
        Resource = [
          "arn:aws:s3:::${var.s3_bucket_name}",
          "arn:aws:s3:::${var.s3_bucket_name}/*"
        ]
      },
      # Secrets Manager permissions
      {
        Effect = "Allow"
        Action = [
          "secretsmanager:GetSecretValue"
        ]
        Resource = var.secrets_manager_arn
      },
      # CloudWatch Logs permissions
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "*"
      },
      # ECR permissions for Fargate
      {
        Effect = "Allow"
        Action = [
          "ecr:GetAuthorizationToken",
          "ecr:BatchCheckLayerAvailability",
          "ecr:GetDownloadUrlForLayer",
          "ecr:BatchGetImage"
        ]
        Resource = "*"
      }
    ]
  })
}

# IAM Role for Batch Service
resource "aws_iam_role" "batch_service_role" {
  name = "ml-pipeline-batch-service-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "batch.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })

  tags = {
    Project     = "ML-Pipeline"
    Environment = "Production"
  }
}

# Attach Batch Service Role Policy to Batch Service Role
resource "aws_iam_role_policy_attachment" "batch_service_role_policy" {
  role       = aws_iam_role.batch_service_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSBatchServiceRole"
}

# IAM Role for EC2 instances
resource "aws_iam_role" "batch_instance_role" {
  name = "ml-pipeline-batch-instance-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })

  tags = {
    Project     = "ML-Pipeline"
    Environment = "Production"
  }
}

# Attach EC2 Container Service Role Policy to Batch Instance Role
resource "aws_iam_role_policy_attachment" "batch_instance_role_policy" {
  role       = aws_iam_role.batch_instance_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonEC2ContainerServiceforEC2Role"
}

# Instance Profile
resource "aws_iam_instance_profile" "batch_instance_profile" {
  name = "ml-pipeline-batch-instance-profile"
  role = aws_iam_role.batch_instance_role.name
}

# Security Group
resource "aws_security_group" "batch_security_group" {
  name        = "ml-pipeline-batch-sg"
  description = "Security group for ML pipeline batch jobs"
  vpc_id      = var.vpc_id

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "ml-pipeline-batch-sg"
    Project     = "ML-Pipeline"
    Environment = "Production"
  }
}

# CloudWatch Log Group
resource "aws_cloudwatch_log_group" "batch_log_group" {
  name              = "/aws/batch/ml-pipeline"
  retention_in_days = 30

  tags = {
    Project     = "ML-Pipeline"
    Environment = "Production"
  }
}

# Batch Compute Environment (Fargate)
resource "aws_batch_compute_environment" "ml_pipeline_fargate" {
  name  = "ml-pipeline-fargate-compute-env"
  type  = "MANAGED"
  state = "ENABLED"

  compute_resources {
    type               = "FARGATE"
    max_vcpus          = 10
    security_group_ids = [aws_security_group.batch_security_group.id]
    subnets            = var.subnet_ids
  }

  tags = {
    Project     = "ML-Pipeline"
    Environment = "Production"
  }
}

# Batch Job Queue
resource "aws_batch_job_queue" "ml_pipeline_queue" {
  name     = "ml-pipeline-job-queue"
  state    = "ENABLED"
  priority = 1

  compute_environment_order {
    order               = 1
    compute_environment = aws_batch_compute_environment.ml_pipeline_fargate.arn
  }

  tags = {
    Project     = "ML-Pipeline"
    Environment = "Production"
  }
}

# Batch Job Definition (Fargate)
resource "aws_batch_job_definition" "ml_pipeline_job" {
  name                 = "ml-pipeline-fargate-job"
  type                 = "container"
  platform_capabilities = ["FARGATE"]

  container_properties = jsonencode({
    image = var.ecr_image_uri
    resourceRequirements = [
      {
        type  = "VCPU"
        value = "2"
      },
      {
        type  = "MEMORY"
        value = "4096"
      }
    ]
    jobRoleArn       = aws_iam_role.batch_job_role.arn
    executionRoleArn = aws_iam_role.batch_job_role.arn
    networkConfiguration = {
      assignPublicIp = "ENABLED"
    }
    logConfiguration = {
      logDriver = "awslogs"
      options = {
        "awslogs-group"  = aws_cloudwatch_log_group.batch_log_group.name
        "awslogs-region" = var.aws_region
      }
    }
    environment = [
      {
        name  = "S3_BUCKET"
        value = var.s3_bucket_name
      }
    ]
  })

  tags = {
    Project     = "ML-Pipeline"
    Environment = "Production"
  }
}

# Outputs
output "job_definition_arn" {
  description = "ARN of the Batch Job Definition"
  value       = aws_batch_job_definition.ml_pipeline_job.arn
}

output "job_queue_arn" {
  description = "ARN of the Batch Job Queue"
  value       = aws_batch_job_queue.ml_pipeline_queue.arn
}

output "compute_environment_arn" {
  description = "ARN of the Batch Compute Environment"
  value       = aws_batch_compute_environment.ml_pipeline_fargate.arn
}

output "batch_job_role_arn" {
  description = "ARN of the Batch Job Role"
  value       = aws_iam_role.batch_job_role.arn
}

output "security_group_id" {
  description = "ID of the Batch Security Group"
  value       = aws_security_group.batch_security_group.id
}

output "log_group_name" {
  description = "Name of the CloudWatch Log Group"
  value       = aws_cloudwatch_log_group.batch_log_group.name
}