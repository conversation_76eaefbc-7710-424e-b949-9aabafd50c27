#------ML-batch-pipeline-variables------
variable "ecr_image_uri" {
description = "ECR image URI for the ML container"
type = string
default = "037590317780.dkr.ecr.us-east-1.amazonaws.com/evertrue/ml-ops:latest"
}

variable "s3_bucket_name" {
description = "S3 bucket for ML outputs"
type = string
default = "evertrue-ml-ops"
}

variable "secrets_manager_arn" {
description = "Secrets Manager ARN for database credentials"
type = string
default = "arn:aws:secretsmanager:us-east-1:037590317780:secret:snowflake-db-ml-credentials-Mo2Ss5"
}

variable "vpc_id" {
description = "Existing VPC ID to use"
type = string
default = "vpc-0f18e01ba80a6ef7e"
}

variable "subnet_ids" {
description = "List of subnet IDs for Batch compute environment"
type = list(string)
default = [
"subnet-00ab0b74fb8f1b3ee",
"subnet-082861aaa73e611be",
"subnet-0cf53551189e58098",
"subnet-0ce485b0562ddd16e"
]
}

variable "aws_region" {
description = "AWS region for resources"
type = string
default = "us-east-1"
}

