# ML Batch Pipeline Variables
variable "ecr_image_uri" {
  description = "ECR image URI for the ML container"
  type        = string
  default     = "478370645242.dkr.ecr.us-east-1.amazonaws.com/evertrue/ml-ops:latest"
}

variable "s3_bucket_name" {
  description = "S3 bucket for ML outputs"
  type        = string
  default     = "et-ml-ops"
}

variable "secrets_manager_arn" {
  description = "Secrets Manager ARN for database credentials"
  type        = string
  default     = "arn:aws:secretsmanager:us-east-1:923017004626:secret:snowflake-db-ml-credentials-xPDaaY"
}

variable "vpc_id" {
  description = "Existing VPC ID to use"
  type        = string
  default     = "vpc-0ff356fad95cc1291"
}

variable "subnet_ids" {
  description = "List of subnet IDs for Batch compute environment"
  type        = list(string)
  default = [
    "subnet-0b8868f279cbd6f93",
    "subnet-00f4d96c1843f0566",
    "subnet-01c67a6f6334ffe49",
    "subnet-046be39fa8e1cad88"
  ]
}

variable "aws_region" {
  description = "AWS region for resources"
  type        = string
  default     = "us-east-1"
}

