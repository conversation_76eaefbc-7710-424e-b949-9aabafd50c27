# Define the IAM Role
resource "aws_iam_role" "netsmartz_contractor_access_role" {
  name = "NetsmartzContractorAccessRole"

  assume_role_policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Effect" : "Allow",
        "Principal" : {
          "AWS" : "arn:aws:iam::640433229668:root"
        },
        "Action" : "sts:AssumeRole"
      }
    ]
  })
}

# Define the IAM Policies
resource "aws_iam_policy" "netsmartz_contractors_ecs_access" {
  name        = "NetsmartzContractorsECSAccess"
  description = "Policy for ECS access"

  policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Sid" : "VisualEditor0",
        "Effect" : "Allow",
        "Action" : [
          "ecs:DescribeTaskDefinition",
          "ecs:ListServices",
          "ecs:DeregisterTaskDefinition",
          "ecs:UpdateService",
          "logs:CreateLogStream",
          "ecs:CreateService",
          "ecs:ListTasks",
          "ecs:RegisterTaskDefinition",
          "elasticloadbalancing:CreateRule",
          "ecs:DescribeServices",
          "ecs:DescribeContainerInstances",
          "ecs:DescribeTasks",
          "elasticloadbalancing:ModifyTargetGroupAttributes",
          "ecs:ListTaskDefinitions",
          "ecs:ListClusters",
          "elasticloadbalancing:CreateTargetGroup",
          "logs:CreateLogGroup",
          "ecs:DescribeClusters",
          "ecs:ListTaskDefinitionFamilies",
          "ecs:ListContainerInstances",
          "logs:PutRetentionPolicy",
          "elasticloadbalancing:ModifyTargetGroup"
        ],
        "Resource" : "*"
      },
      {
        "Sid" : "VisualEditor1",
        "Effect" : "Allow",
        "Action" : [
          "iam:GetRole",
          "iam:PassRole"
        ],
        "Resource" : [
          "arn:aws:iam::923017004626:role/ecs-api-task-role",
          "arn:aws:iam::923017004626:role/ecs-worker-task-role",
          "arn:aws:iam::923017004626:role/ecs-api-service-role",
          "arn:aws:iam::923017004626:role/ecs-worker-service-role",
          "arn:aws:iam::923017004626:role/ecs-importer-sftp-task-role"
        ]
      },
      {
        "Sid" : "VisualEditor2",
        "Effect" : "Allow",
        "Action" : [
          "iam:GetRole",
          "iam:PassRole"
        ],
        "Resource" : "arn:aws:iam::923017004626:role/ecs-worker-task-role"
      }
    ]
  })
}

resource "aws_iam_policy" "netsmartz_contractors_ecs_run_task" {
  name        = "NetsmartzContractorsECSRunTask"
  description = "Policy for ECS Run Task"

  policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Effect" : "Allow",
        "Action" : [
          "ecs:RunTask",
          "ecs:StopTask"
        ],
        "Resource" : "*"
      }
    ]
  })
}

resource "aws_iam_policy" "netsmartz_contractors_emr_run_policy" {
  name        = "NetsmartzContractorsEMRRunPolicy"
  description = "Policy for EMR Run"

  policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Sid" : "VisualEditor0",
        "Effect" : "Allow",
        "Action" : "iam:PassRole",
        "Resource" : [
          "arn:aws:iam::923017004626:role/EMR_DefaultRole",
          "arn:aws:iam::923017004626:role/EMR_EC2_DefaultRole"
        ]
      },
      {
        "Sid" : "VisualEditor1",
        "Effect" : "Allow",
        "Action" : "elasticmapreduce:*",
        "Resource" : "*"
      }
    ]
  })
}

resource "aws_iam_policy" "netsmartz_contractors_system_manager_access" {
  name        = "NetsmartzContractorsSystemManagerAccessPolicy"
  description = "Policy for System Manager Access"

  policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Sid" : "VisualEditor0",
        "Effect" : "Allow",
        "Action" : [
          "ssm:ListCommands",
          "ssm:ListDocumentVersions",
          "ssm:DescribeInstancePatches",
          "ssm:ListInstanceAssociations",
          "ssm:GetParameter",
          "ssm:GetMaintenanceWindowExecutionTaskInvocation",
          "ssm:StartSession",
          "ssm:DescribeAutomationExecutions",
          "ssm:GetMaintenanceWindowTask",
          "ssm:DescribeMaintenanceWindowExecutionTaskInvocations",
          "ssm:DescribeAutomationStepExecutions",
          "ssm:DescribeParameters",
          "ssm:ListResourceDataSync",
          "ssm:ListDocuments",
          "ssm:ListComplianceItems",
          "ssm:GetConnectionStatus",
          "ssm:GetMaintenanceWindowExecutionTask",
          "ssm:GetMaintenanceWindowExecution",
          "ssm:ListResourceComplianceSummaries",
          "ssm:GetParameters",
          "ssm:DescribeMaintenanceWindows",
          "ssm:DescribeEffectivePatchesForPatchBaseline",
          "ssm:DescribeAssociationExecutions",
          "ssm:DescribeDocumentPermission",
          "ssm:ListCommandInvocations",
          "ssm:GetAutomationExecution",
          "ssm:DescribePatchGroups",
          "ssm:GetDefaultPatchBaseline",
          "ssm:DescribeDocument",
          "ssm:DescribeMaintenanceWindowTasks",
          "ssm:ListAssociationVersions",
          "ssm:GetPatchBaselineForPatchGroup",
          "ssm:PutConfigurePackageResult",
          "ssm:TerminateSession",
          "ssm:DescribePatchGroupState",
          "ssm:DescribeMaintenanceWindowExecutions",
          "ssm:GetManifest",
          "ssm:DescribeMaintenanceWindowExecutionTasks",
          "ssm:DescribeInstancePatchStates",
          "ssm:DescribeInstancePatchStatesForPatchGroup",
          "ssm:GetDocument",
          "ssm:GetInventorySchema",
          "ssm:GetParametersByPath",
          "ssm:GetMaintenanceWindow",
          "ssm:DescribeInstanceAssociationsStatus",
          "ssm:DescribeAssociationExecutionTargets",
          "ssm:GetPatchBaseline",
          "ssm:DescribeInstanceProperties",
          "ssm:ListInventoryEntries",
          "ssm:DescribeAssociation",
          "ssm:GetDeployablePatchSnapshotForInstance",
          "ssm:DescribeSessions",
          "ssm:GetParameterHistory",
          "ssm:DescribeMaintenanceWindowTargets",
          "ssm:DescribePatchBaselines",
          "ssm:DescribeEffectiveInstanceAssociations",
          "ssm:GetInventory",
          "ssm:DescribeActivations",
          "ssm:GetCommandInvocation",
          "ssm:ListComplianceSummaries",
          "ssm:DescribeInstanceInformation",
          "ssm:ListTagsForResource",
          "ssm:DescribeDocumentParameters",
          "ssm:ListAssociations",
          "ssm:DescribeAvailablePatches"
        ],
        "Resource" : "*"
      }
    ]
  })
}

# Attach Policies to Role
resource "aws_iam_role_policy_attachment" "netsmartz_contractors_ecs_access_attach" {
  role       = aws_iam_role.netsmartz_contractor_access_role.name
  policy_arn = aws_iam_policy.netsmartz_contractors_ecs_access.arn
}

resource "aws_iam_role_policy_attachment" "netsmartz_contractors_ecs_run_task_attach" {
  role       = aws_iam_role.netsmartz_contractor_access_role.name
  policy_arn = aws_iam_policy.netsmartz_contractors_ecs_run_task.arn
}

resource "aws_iam_role_policy_attachment" "netsmartz_contractors_emr_run_policy_attach" {
  role       = aws_iam_role.netsmartz_contractor_access_role.name
  policy_arn = aws_iam_policy.netsmartz_contractors_emr_run_policy.arn
}

resource "aws_iam_role_policy_attachment" "netsmartz_contractors_system_manager_access_attach" {
  role       = aws_iam_role.netsmartz_contractor_access_role.name
  policy_arn = aws_iam_policy.netsmartz_contractors_system_manager_access.arn
}

resource "aws_iam_role_policy_attachment" "netsmartz_contractors_read_only_access_attach" {
  role       = aws_iam_role.netsmartz_contractor_access_role.name
  policy_arn = "arn:aws:iam::aws:policy/ReadOnlyAccess"
}
