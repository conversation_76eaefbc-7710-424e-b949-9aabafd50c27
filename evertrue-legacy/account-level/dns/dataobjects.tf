data "aws_route53_zone" "evertrue" {
  name         = "evertrue.com"
  private_zone = false
}

data "aws_route53_zone" "priv_evertrue" {
  name         = "priv.evertrue.com"
  private_zone = true
}

data "aws_lb" "shared-infra-prod-host" {
  provider = aws.evertrueprod
  arn      = "arn:aws:elasticloadbalancing:us-east-1:923017004626:loadbalancer/app/shared-infra-prod-host/af9d6426d2448d8f"
  name     = "shared-infra-prod-host"
}
