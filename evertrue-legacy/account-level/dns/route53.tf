locals {
  evertrue_records = {
    "f6335b._domainkey" = {
      name = "f6335b._domainkey",
      records = [
        "v=DKIM1;k=rsa;p=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAptBTyEXJHZVp1RrSlp0wcoiN2FesnVBZqDQW8lscyJ421yI1WelxxxMqS801khwJ/KcuSNveqg1gazHiQ59eLW1v0zHJPjfo6xMsyrc0N4FnCCuFH0TfqJRu6ADLeOjojkDi34AUYGP+bW3mf0oUluK9yBZoDVTh1k3cJD2Z6uXx1LHVUYNmXU68IZYA4+2+vnN\"\"7B4L8PAMAppHkeZ6x36Rklnya746XUCulRBmUxpBpT8/B9sOz18pF8ESjLoD5qC1OQAZodMO8uPB/cwYsBMtllDn7A1TS/aisyX+BUw7UZ3esBSfbhx69mxxSrOyx4q8wap0kMvi/HMvVRC5ghQIDAQAB"
      ],
      type = "TXT",
      ttl  = "3600"
    }
    "transfer" = {
      name = "transfer",
      records = [
        "transfer.evertrue.com.ss-domain.com."
      ],
      type = "CNAME",
      ttl  = "60"
    }
    "static-transfer" = {
      name = "static-transfer",
      records = [
        "static-transfer.evertrue.com.ss-domain.com."
      ],
      type = "CNAME",
      ttl  = "60"
    }
    "_123ba51047c9433883e78ae88abbd2a2.transfer" = {
      name = "_123ba51047c9433883e78ae88abbd2a2.transfer",
      records = [
        "_a38bfe6716e361598bf3c624f5a88bce.sggfvksfyf.acm-validations.aws."
      ],
      type = "CNAME",
      ttl  = "60"
    }
    "_38b42e24cd85d289faa45e6faf1d2797.static-transfer" = {
      name = "_38b42e24cd85d289faa45e6faf1d2797.static-transfer",
      records = [
        "_98688fa29a23314ca86ebb20082159d3.sggfvksfyf.acm-validations.aws."
      ],
      type = "CNAME",
      ttl  = "60"
    }
    "_9F2B9437A0C02F698C37EBC407DDE4F1" = {
      name = "_9F2B9437A0C02F698C37EBC407DDE4F1",
      records = [
        "175C3023D30EB18F5F593FD73BA0C21B.C4652F64794E6B3E7A2925860BE8F4D9.642ee44227cae.comodoca.com."
      ],
      type = "CNAME",
      ttl  = "60"
    }
    "learn" = {
      name = "learn",
      records = [
        "${data.aws_lb.shared-infra-prod-host.dns_name}."
      ],
      type = "CNAME",
      ttl  = "60"
    }
    "grow" = {
      name = "grow",
      records = [
        "evertrueinc.mktoweb.com."
      ],
      type = "CNAME",
      ttl  = "60"
    }
    "click" = {
      name = "click",
      records = [
        "mkto-ab610169.com."
      ],
      type = "CNAME",
      ttl  = "60"
    }
    "m1._domainkey" = {
      name = "m1._domainkey",
      records = [
        "v=DKIM1;k=rsa;p=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQClTiFIHFkgDVNCtyaH+MQA4ZXM+8XORaoXILmgevWe65v7vUBEtJ9o6MjnydXTpjFeWCSM8DlyLy12u7x1i02kDUV8mf+/8s6VEBYnkEaVWuqxSP6uNyFW3K5UWNewMGmE1wCTIAtM3HBBF4u8x1chJK0ZF/iPpR3CTu+qOh1skQIDAQAB"
      ],
      type = "TXT",
      ttl  = "60"
    }
    "evertrue_TXT" = {
      name = "",
      records = [
        "v=spf1 include:stspg-customer.com include:smtp1.uservoice.com include:_spf.google.com include:sendgrid.net include:_spf.salesforce.com include:_spf.hubspot.com include:spf.mandrillapp.com include:help\"\"scoutemail.com include:docebosaas.com include:mktomail.com ~all",
        "google-site-verification=07kOWaNwB27sx4xlT5tEPwqBFRnZfzbguxxVWC57s6M",
        "google-site-verification=cpVdiV63x8SQcSOhkhKGLrQUT_EYffZ9ODWjRuFeS2M",
        "status-page-domain-verification=2kg03zd8s3b5",
        "ZOOM_verify_xocrAFPOT7WfUvPCOHHT5w",
        "7501BBA228",
        "adobe-idp-site-verification=e08e99b4040854ed923badb260fc620187c0ba3de6de8a7ade39eb5d24cf67d9",
        "MS=2AAA31E85345235506FFA586F8D1F510F54C0458"
      ],
      type = "TXT",
      ttl  = "60"
    }
    "evertrue_A" = {
      name = "",
      records = [
        "**************",
        "141.193.213.11"
      ],
      type = "A",
      ttl  = "60"
    }
    "www" = {
      name = "www",
      records = [
        "evertrue.com."
      ],
      type = "CNAME",
      ttl  = "60"
    }
    "record" = {
      name = "prod-ai.rds",
      records = [
        "prod-ai.cbhvqdjzngrf.us-east-1.rds.amazonaws.com."
      ],
      type = "CNAME",
      ttl  = "60"
    }
    # "record" = {
    #   name = "",
    #   records = [
    #     ""
    #   ],
    #   type = "",
    #   ttl  = "60"
    # }
  }
  priv_evertrue_records = {

  }
}
module "evertrue_records" {
  source  = "terraform-aws-modules/route53/aws//modules/records"
  version = "~> 2.0"

  for_each = local.evertrue_records

  records   = [each.value]
  zone_name = data.aws_route53_zone.evertrue.name
}

module "priv_evertrue_records" {
  source  = "terraform-aws-modules/route53/aws//modules/records"
  version = "~> 2.0"

  for_each = local.priv_evertrue_records

  records   = [each.value]
  zone_name = data.aws_route53_zone.priv_evertrue.name
}
