################################################################################
################################################################################
# Let's Encrypt registration
################################################################################
################################################################################
resource "acme_registration" "evertrue_com_reg" {
  account_key_pem = jsondecode(data.aws_secretsmanager_secret_version.evertrue_com_acme_reg_private_key_current.secret_string)["reg_key"]
  email_address   = "<EMAIL>"
}

################################################################################
# watch.evertrue.com
################################################################################
# CSR for watch.evertrue.com
# Obtained from Vidyard, via Teddy Cook, stored in watch_evertrue_com.csr

# Signed certificate from Let's Encrypt
resource "acme_certificate" "watch_evertrue_com_certificate" {
  account_key_pem         = acme_registration.evertrue_com_reg.account_key_pem
  certificate_request_pem = file("./watch_evertrue_com.csr")

  dns_challenge {
    provider = "route53"

    config = {
      AWS_PROFILE        = "evertrue"
      AWS_HOSTED_ZONE_ID = "Z20FI4RWVXAMXL"
      AWS_TTL            = "60"
    }
  }
}

################################################################################
# truelearning.evertrue.com
################################################################################
# CSR for truelearning.evertrue.com
# Obtained from Fig Learning, via Katie McDermott, stored in truelearning_evertrue_com.csr

# Signed certificate from Let's Encrypt
resource "acme_certificate" "truelearning_evertrue_com_certificate" {
  account_key_pem         = acme_registration.evertrue_com_reg.account_key_pem
  certificate_request_pem = file("./truelearning_evertrue_com.csr")

  dns_challenge {
    provider = "route53"

    config = {
      AWS_PROFILE        = "evertrue"
      AWS_HOSTED_ZONE_ID = "Z20FI4RWVXAMXL"
      AWS_TTL            = "60"
    }
  }
}

