################################################################################
# Backend Configuration
################################################################################
terraform {
  # S3 Backend in Tools account
  backend "s3" {
    bucket         = "et-devops-tools"
    region         = "us-east-1"
    dynamodb_table = "et-devops-terraform-state-lock"
    acl            = "private"
    key            = "terraform/************/account-level/ssl/terraform.tfstate"
    encrypt        = "true"
    profile        = "evertruetools"
  }
}

