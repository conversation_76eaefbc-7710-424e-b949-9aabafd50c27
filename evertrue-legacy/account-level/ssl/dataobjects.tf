################################################################################
################################################################################
# Data Objects
################################################################################
################################################################################
################################################################################
# SSL Private keys
################################################################################
data "aws_secretsmanager_secret_version" "evertrue_com_acme_reg_private_key_current" {
  secret_id = module.secrets-manager["evertrue_com_acme_reg_private_key"].secret_ids.evertrue_com_acme_reg_private_key
}
