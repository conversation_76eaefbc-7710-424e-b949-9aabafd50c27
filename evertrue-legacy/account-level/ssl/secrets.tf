locals {
  secrets = {
    evertrue_com_acme_reg_private_key = {
      secrets = {
        evertrue_com_acme_reg_private_key = {
          description             = "Private key for evertrue.com ACME (Let's Encrypt) registration"
          recovery_window_in_days = 7
          secret_string           = "Fake initial value"

        }
      }
      tags = {
        Owner       = "DevOps team"
        Environment = "prod"
        Terraform   = true

      }
    }
  }
}

module "secrets-manager" {
  source  = "lgallard/secrets-manager/aws"
  version = "~> 0.8"

  for_each = local.secrets

  secrets   = try(each.value.secrets, {})
  unmanaged = true
  tags      = try(each.value.tags, {})
}
