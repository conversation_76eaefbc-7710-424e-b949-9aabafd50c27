# Terraform @ Evertrue

This is the repo for our [Terraform](https://www.terraform.io) provisioning scripts.

# What you need

* You will need to [get tfenv](https://github.com/tfutils/tfenv) and then install the latest 1.3.x version
    * `tfenv install latest:^1.3`
* You will also need an AWS profile with write access to the `et-devops-tools` bucket (used by Terraform to keep state).

# How to use

First a few basics:

> Note that not all services in all environments yet have provisioning scripts built for them so if you don't see something here, chances are it doesn't exist yet and you are going to have to write it yourself.

Once you've got the prerequisites in place, you can change to a service directory (e.g. `evertrue-legcacy/account-level`) and run:
```
terraform state list
```
to see if everything is working.

# How it works

TBD
